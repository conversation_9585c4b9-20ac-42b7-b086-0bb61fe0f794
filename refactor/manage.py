#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import os
import sys
import threading
import webbrowser


def open_browser():
    """Open browser after server starts."""
    threading.Timer(1, webbrowser.open, args=['http://localhost:8080/admin/dist/index.html']).start()


def main():
    """Run administrative tasks."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'orchard_system.settings')
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    
    # Open browser for development
    if len(sys.argv) > 1 and sys.argv[1] == 'runserver':
        open_browser()
    
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    main()
