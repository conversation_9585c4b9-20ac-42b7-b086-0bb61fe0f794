"""
URL configuration for orchard_system project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('admin/', admin.site.urls),
    
    # API endpoints - maintaining original interface addresses
    path('users/', include('apps.users.urls')),
    path('config/', include('apps.core.urls')),
    path('nonghu/', include('apps.farmers.urls')),
    path('yonghu/', include('apps.users.urls')),
    path('guoyuanxinxi/', include('apps.orchards.urls')),
    path('guoyuanzhuangtai/', include('apps.orchards.urls')),
    path('guopinxinxi/', include('apps.products.urls')),
    path('cangchu/', include('apps.warehouse.urls')),
    path('ruku/', include('apps.warehouse.urls')),
    path('chuku/', include('apps.warehouse.urls')),
    path('wuliu/', include('apps.logistics.urls')),
    path('zhiliangdengji/', include('apps.quality.urls')),
    
    # Common endpoints
    path('file/', include('apps.core.urls')),
    path('upload/', include('apps.core.urls')),
    path('cal/', include('apps.core.urls')),
    path('follow/', include('apps.core.urls')),
    path('location/', include('apps.core.urls')),
    path('matchFace/', include('apps.core.urls')),
    path('option/', include('apps.core.urls')),
    path('sh/', include('apps.core.urls')),
    path('group/', include('apps.core.urls')),
    path('value/', include('apps.core.urls')),
    path('spider/', include('apps.core.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
