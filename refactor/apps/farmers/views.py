"""
Farmer views.
"""
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from apps.core.utils import ResponseUtil, JWTUtil, PasswordUtil
from apps.core.constants import (
    NORMAL_CODE, CRUD_ERROR_CODE, PASSWORD_ERROR_CODE, 
    ID_EXIST_CODE, DEFAULT_PASSWORD
)
from .models import Farmer
from .serializers import FarmerSerializer, FarmerCreateSerializer, FarmerLoginSerializer


@csrf_exempt
@require_http_methods(["POST"])
def farmer_register(request):
    """Farmer registration."""
    try:
        req_dict = request.session.get('req_dict', {})
        
        # Check if account exists
        if Farmer.objects.filter(account=req_dict.get('account')).exists():
            return ResponseUtil.error(ID_EXIST_CODE, "Account already exists")
        
        # Hash password
        if 'password' in req_dict:
            req_dict['password'] = PasswordUtil.hash_password(req_dict['password'])
        
        # Create farmer
        farmer = Farmer.objects.create(**req_dict)
        return ResponseUtil.success({'id': farmer.id})
    except Exception as e:
        return ResponseUtil.error(CRUD_ERROR_CODE, str(e))


@csrf_exempt
@require_http_methods(["POST"])
def farmer_login(request):
    """Farmer login."""
    try:
        req_dict = request.session.get('req_dict', {})
        account = req_dict.get('account') or req_dict.get('zhanghao')
        password = req_dict.get('password') or req_dict.get('mima')
        
        if not account or not password:
            return ResponseUtil.error(PASSWORD_ERROR_CODE, "Account and password required")
        
        # Find farmer
        farmer = Farmer.objects.filter(account=account).first()
        if not farmer:
            return ResponseUtil.error(PASSWORD_ERROR_CODE, "Invalid account or password")
        
        # Verify password
        if not PasswordUtil.verify_password(password, farmer.password):
            return ResponseUtil.error(PASSWORD_ERROR_CODE, "Invalid account or password")
        
        # Generate token
        farmer_data = {
            'id': farmer.id,
            'account': farmer.account,
            'name': farmer.name,
            'tablename': 'nonghu'
        }
        token = JWTUtil.generate_token(farmer_data)
        
        # Store in session
        request.session['token'] = token
        request.session['params'] = farmer_data
        
        return ResponseUtil.success({'token': token, 'farmer': farmer_data})
    except Exception as e:
        return ResponseUtil.error(CRUD_ERROR_CODE, str(e))


@csrf_exempt
@require_http_methods(["POST", "GET"])
def farmer_logout(request):
    """Farmer logout."""
    try:
        request.session.flush()
        return ResponseUtil.success(message="Logout successful")
    except Exception as e:
        return ResponseUtil.error(CRUD_ERROR_CODE, str(e))


@csrf_exempt
@require_http_methods(["POST"])
def farmer_reset_password(request):
    """Reset farmer password."""
    try:
        req_dict = request.session.get('req_dict', {})
        account = req_dict.get('account') or req_dict.get('username')
        
        if not account:
            return ResponseUtil.error(CRUD_ERROR_CODE, "Account required")
        
        farmer = Farmer.objects.filter(account=account).first()
        if not farmer:
            return ResponseUtil.error(CRUD_ERROR_CODE, "Farmer not found")
        
        new_password = PasswordUtil.hash_password(DEFAULT_PASSWORD)
        farmer.password = new_password
        farmer.save()
        
        return ResponseUtil.success(message="Password reset successful")
    except Exception as e:
        return ResponseUtil.error(CRUD_ERROR_CODE, str(e))


@csrf_exempt
@require_http_methods(["GET"])
def farmer_session(request):
    """Get current farmer session."""
    try:
        farmer_data = request.session.get('params')
        if not farmer_data:
            return ResponseUtil.error(PASSWORD_ERROR_CODE, "Not logged in")
        
        farmer = Farmer.objects.get(id=farmer_data['id'])
        serializer = FarmerSerializer(farmer)
        return ResponseUtil.success(serializer.data)
    except Farmer.DoesNotExist:
        return ResponseUtil.error(CRUD_ERROR_CODE, "Farmer not found")
    except Exception as e:
        return ResponseUtil.error(CRUD_ERROR_CODE, str(e))


@csrf_exempt
@require_http_methods(["GET"])
def farmer_page(request):
    """Get farmers with pagination."""
    try:
        req_dict = request.session.get('req_dict', {})
        page = int(req_dict.get('page', 1))
        limit = int(req_dict.get('limit', 10))
        
        queryset = Farmer.objects.all()
        
        # Apply filters
        if 'account' in req_dict:
            queryset = queryset.filter(account__icontains=req_dict['account'])
        if 'name' in req_dict:
            queryset = queryset.filter(name__icontains=req_dict['name'])
        
        # Apply sorting
        sort_field = req_dict.get('sort', 'id')
        order = req_dict.get('order', 'asc')
        if order == 'desc':
            sort_field = f'-{sort_field}'
        queryset = queryset.order_by(sort_field)
        
        # Pagination
        total = queryset.count()
        start = (page - 1) * limit
        end = start + limit
        farmers = queryset[start:end]
        
        serializer = FarmerSerializer(farmers, many=True)
        data = ResponseUtil.paginated_response(serializer.data, page, limit, total)
        return ResponseUtil.success(data)
    except Exception as e:
        return ResponseUtil.error(CRUD_ERROR_CODE, str(e))


@csrf_exempt
@require_http_methods(["GET"])
def farmer_list(request):
    """Get farmer list."""
    try:
        farmers = Farmer.objects.all()
        serializer = FarmerSerializer(farmers, many=True)
        return ResponseUtil.success(serializer.data)
    except Exception as e:
        return ResponseUtil.error(CRUD_ERROR_CODE, str(e))


@csrf_exempt
@require_http_methods(["POST"])
def farmer_save(request):
    """Save new farmer."""
    try:
        req_dict = request.session.get('req_dict', {})
        
        # Map old field names to new ones
        if 'zhanghao' in req_dict:
            req_dict['account'] = req_dict.pop('zhanghao')
        if 'mima' in req_dict:
            req_dict['password'] = req_dict.pop('mima')
        if 'nonghuxingming' in req_dict:
            req_dict['name'] = req_dict.pop('nonghuxingming')
        if 'touxiang' in req_dict:
            req_dict['avatar'] = req_dict.pop('touxiang')
        if 'xingbie' in req_dict:
            req_dict['gender'] = req_dict.pop('xingbie')
        if 'nianling' in req_dict:
            req_dict['age'] = req_dict.pop('nianling')
        if 'youxiang' in req_dict:
            req_dict['email'] = req_dict.pop('youxiang')
        if 'dianhua' in req_dict:
            req_dict['phone'] = req_dict.pop('dianhua')
        
        # Check if account exists
        if Farmer.objects.filter(account=req_dict.get('account')).exists():
            return ResponseUtil.error(ID_EXIST_CODE, "Account already exists")
        
        # Hash password
        if 'password' in req_dict:
            req_dict['password'] = PasswordUtil.hash_password(req_dict['password'])
        
        farmer = Farmer.objects.create(**req_dict)
        return ResponseUtil.success({'id': farmer.id})
    except Exception as e:
        return ResponseUtil.error(CRUD_ERROR_CODE, str(e))


@csrf_exempt
@require_http_methods(["POST"])
def farmer_add(request):
    """Add new farmer (frontend)."""
    return farmer_save(request)


@csrf_exempt
@require_http_methods(["GET"])
def farmer_info(request, id_):
    """Get farmer by ID."""
    try:
        farmer = Farmer.objects.get(id=id_)
        serializer = FarmerSerializer(farmer)
        return ResponseUtil.success(serializer.data)
    except Farmer.DoesNotExist:
        return ResponseUtil.error(CRUD_ERROR_CODE, "Farmer not found")
    except Exception as e:
        return ResponseUtil.error(CRUD_ERROR_CODE, str(e))


@csrf_exempt
@require_http_methods(["GET"])
def farmer_detail(request, id_):
    """Get farmer detail by ID."""
    return farmer_info(request, id_)


@csrf_exempt
@require_http_methods(["POST"])
def farmer_update(request):
    """Update farmer."""
    try:
        req_dict = request.session.get('req_dict', {})
        farmer_id = req_dict.pop('id')
        
        # Map old field names to new ones
        if 'zhanghao' in req_dict:
            req_dict['account'] = req_dict.pop('zhanghao')
        if 'mima' in req_dict:
            req_dict['password'] = req_dict.pop('mima')
        if 'nonghuxingming' in req_dict:
            req_dict['name'] = req_dict.pop('nonghuxingming')
        if 'touxiang' in req_dict:
            req_dict['avatar'] = req_dict.pop('touxiang')
        if 'xingbie' in req_dict:
            req_dict['gender'] = req_dict.pop('xingbie')
        if 'nianling' in req_dict:
            req_dict['age'] = req_dict.pop('nianling')
        if 'youxiang' in req_dict:
            req_dict['email'] = req_dict.pop('youxiang')
        if 'dianhua' in req_dict:
            req_dict['phone'] = req_dict.pop('dianhua')
        
        # Check account uniqueness if updating account
        if 'account' in req_dict:
            if Farmer.objects.exclude(id=farmer_id).filter(account=req_dict['account']).exists():
                return ResponseUtil.error(ID_EXIST_CODE, "Account already exists")
        
        # Hash password if updating
        if 'password' in req_dict:
            req_dict['password'] = PasswordUtil.hash_password(req_dict['password'])
        
        Farmer.objects.filter(id=farmer_id).update(**req_dict)
        return ResponseUtil.success()
    except Exception as e:
        return ResponseUtil.error(CRUD_ERROR_CODE, str(e))


@csrf_exempt
@require_http_methods(["POST"])
def farmer_delete(request):
    """Delete farmer."""
    try:
        req_dict = request.session.get('req_dict', {})
        Farmer.objects.filter(id=req_dict['id']).delete()
        return ResponseUtil.success()
    except Exception as e:
        return ResponseUtil.error(CRUD_ERROR_CODE, str(e))
