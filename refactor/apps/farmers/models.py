"""
Farmer models.
"""
from django.db import models
from apps.core.models import BaseModel
from apps.core.constants import GENDER_CHOICES


class Farmer(BaseModel):
    """
    Farmer model (nonghu).
    """
    account = models.CharField(max_length=255, unique=True, verbose_name='Account')
    password = models.CharField(max_length=255, verbose_name='Password')
    name = models.Char<PERSON>ield(max_length=255, verbose_name='Farmer Name')
    avatar = models.TextField(verbose_name='Avatar')
    gender = models.Char<PERSON>ield(max_length=255, choices=GENDER_CHOICES, verbose_name='Gender')
    age = models.IntegerField(null=True, blank=True, verbose_name='Age')
    email = models.CharField(max_length=255, verbose_name='Email')
    phone = models.CharField(max_length=255, verbose_name='Phone')

    class Meta:
        db_table = 'nonghu'
        verbose_name = 'Farmer'
        verbose_name_plural = 'Farmers'

    def __str__(self):
        return f"{self.name} ({self.account})"
