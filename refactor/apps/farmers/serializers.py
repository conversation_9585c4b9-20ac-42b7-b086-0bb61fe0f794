"""
Farmer serializers.
"""
from rest_framework import serializers
from .models import Farmer


class FarmerSerializer(serializers.ModelSerializer):
    """Farmer serializer."""
    
    class Meta:
        model = Farmer
        fields = ['id', 'account', 'name', 'avatar', 'gender', 'age', 'email', 'phone', 'created_at']
        read_only_fields = ['id', 'created_at']


class FarmerCreateSerializer(serializers.ModelSerializer):
    """Farmer creation serializer."""
    
    class Meta:
        model = Farmer
        fields = ['account', 'password', 'name', 'avatar', 'gender', 'age', 'email', 'phone']


class FarmerLoginSerializer(serializers.Serializer):
    """Farmer login serializer."""
    account = serializers.CharField(max_length=255)
    password = serializers.Char<PERSON>ield(max_length=255)
