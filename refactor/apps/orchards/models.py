"""
Orchard models.
"""
from django.db import models
from apps.core.models import BaseModel


class OrchardInfo(BaseModel):
    """
    Orchard information model (guoyuanxinxi).
    """
    orchard_code = models.CharField(max_length=255, unique=True, verbose_name='Orchard Code')
    orchard_name = models.CharField(max_length=255, verbose_name='Orchard Name')
    orchard_scale = models.CharField(max_length=255, blank=True, verbose_name='Orchard Scale')
    orchard_area = models.CharField(max_length=255, blank=True, verbose_name='Orchard Area')
    orchard_location = models.CharField(max_length=255, blank=True, verbose_name='Orchard Location')
    planting_situation = models.TextField(verbose_name='Planting Situation')
    farmer_account = models.CharField(max_length=255, verbose_name='Farmer Account')
    farmer_name = models.CharField(max_length=255, blank=True, verbose_name='Farmer Name')

    class Meta:
        db_table = 'guoyuanxinxi'
        verbose_name = 'Orchard Information'
        verbose_name_plural = 'Orchard Information'

    def __str__(self):
        return f"{self.orchard_name} ({self.orchard_code})"


class OrchardStatus(BaseModel):
    """
    Orchard status model (guoyuanzhuangtai).
    """
    orchard_name = models.CharField(max_length=255, blank=True, verbose_name='Orchard Name')
    orchard_area = models.CharField(max_length=255, blank=True, verbose_name='Orchard Area')
    orchard_location = models.CharField(max_length=255, blank=True, verbose_name='Orchard Location')
    fertilizer_amount = models.FloatField(verbose_name='Fertilizer Amount (g)')
    water_amount = models.FloatField(verbose_name='Water Amount (ml)')
    planting_status = models.TextField(verbose_name='Planting Status')
    farmer_account = models.CharField(max_length=255, blank=True, verbose_name='Farmer Account')
    farmer_name = models.CharField(max_length=255, blank=True, verbose_name='Farmer Name')
    record_time = models.DateTimeField(null=True, blank=True, verbose_name='Record Time')

    class Meta:
        db_table = 'guoyuanzhuangtai'
        verbose_name = 'Orchard Status'
        verbose_name_plural = 'Orchard Status'

    def __str__(self):
        return f"{self.orchard_name} - {self.record_time}"
